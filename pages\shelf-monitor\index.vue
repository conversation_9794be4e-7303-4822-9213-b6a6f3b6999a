<template>
  <view class="container">
    <!-- 用户信息栏 -->
    <view class="user-info">
      <view class="user-avatar">
        <text class="avatar-text">{{ userInfo.nickname ? userInfo.nickname.charAt(0) : 'U' }}</text>
      </view>
      <view class="user-details">
        <view class="user-name">{{ userInfo.nickname || userInfo.username || '用户' }}</view>
        <view class="user-phone">{{ userInfo.mobile || '' }}</view>
      </view>
      <view class="user-actions">
        <uv-button 
          type="info" 
          size="small" 
          @click="handleLogout"
          :customStyle="{borderRadius: '100px'}"
          text="退出"
        >
        </uv-button>
      </view>
    </view>
    
    <!-- 顶部状态卡片 -->
    <view class="status-section">
      <view class="section-header">
        <text class="section-title">数据概览</text>
        <text class="section-subtitle">实时统计数据</text>
      </view>
      <view class="status-cards">
        <view class="status-card">
          <view class="card-title">平台配置</view>
          <view class="card-value">{{ platformStats.total }}</view>
          <view class="card-desc">{{ platformStats.active }}个已激活</view>
        </view>
        <view class="status-card">
          <view class="card-title">货架总数</view>
          <view class="card-value">{{ shelfStats.total }}</view>
          <view class="card-desc">{{ shelfStats.active }}个监控中</view>
        </view>
        <view class="status-card">
          <view class="card-title">出租中</view>
          <view class="card-value">{{ shelfStats.rented }}</view>
          <view class="card-desc">{{ shelfStats.available }}个可租</view>
        </view>
      </view>
    </view>
    
    <!-- 平台状态 -->
    <view class="platform-section">
      <view class="section-header">
        <text class="section-title">平台状态</text>
        <text class="section-subtitle">各平台登录状态</text>
      </view>
      <view class="platform-list">
        <view
          v-for="platform in platformList"
          :key="platform._id"
          class="platform-item"
        >
          <view class="platform-info">
            <view class="platform-info-left">
              <view class="platform-header">
                <view class="platform-name">{{ platform.platform_name }}</view>
              </view>
              <view class="platform-meta">
                <text class="meta-text">最后登录：{{ formatTime(platform.last_login_time) }}</text>
              </view>
            </view>
            <view class="platform-status">
              <text class="status-dot" :class="getStatusClass(platform.login_status)"></text>
              <text class="status-text">{{ getStatusText(platform.login_status) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 最近日志 -->
    <view class="logs-section">
      <view class="section-header">
        <view class="header-left">
          <text class="section-title">最近日志</text>
          <text class="section-subtitle">最新操作记录</text>
        </view>
        <text class="more-link" @click="goToLogs">查看更多 </text>
      </view>
      <view class="log-list">
        <view
          v-for="log in recentLogs"
          :key="log._id"
          class="log-item"
        >
          <view class="log-content">
            <view class="log-header">
              <view class="log-title">{{ getActionText(log.action) }}</view>
              <view class="log-status" :class="log.status ? 'success' : 'error'">
                {{ log.status ? '成功' : '失败' }}
              </view>
            </view>
            <view class="log-desc">
              <text class="log-platform">{{ getPlatformName(log.platform_type) }}</text>
              <text class="log-separator">·</text>
              <text class="log-account">{{ log.game_account }}</text>
            </view>
            <view class="log-time">{{ formatTime(log.create_time) }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 简化版tabBar -->
    <simple-tabbar
      :current="currentTabIndex"
      @change="onTabChange"
    ></simple-tabbar>
  </view>
</template>

<script>
import { getUserInfo, logout } from '@/utils/auth.js'
import { callFunction } from '@/utils/request.js'
import { StateManager } from '@/common/js/state-manager.js'
import simpleTabBarMixin from '@/mixins/simple-tabbar-mixin.js'

export default {
  name: 'ShelfMonitor',
  mixins: [simpleTabBarMixin],
  data() {
    return {
      userInfo: {},
      platformStats: {
        total: 0,
        active: 0
      },
      shelfStats: {
        total: 0,
        active: 0,
        rented: 0,
        available: 0
      },
      platformList: [],
      recentLogs: [],
      currentTabIndex: 0 // 监控页面是第1个tab，索引为0
    }
  },
  onShow() {
    this.loadData()
  },
  onLoad() {
    // 获取用户信息
    this.userInfo = getUserInfo()
  },
  methods: {
    async loadData() {
      try {
        await Promise.all([
          this.loadShelfStats(),
          this.loadPlatformList(),
          this.loadRecentLogs()
        ])
      } catch (error) {
        console.error('加载数据失败:', error)
        uni.showToast({
          title: '加载数据失败',
          icon: 'error'
        })
      }
    },
    async loadShelfStats() {
      try {
        const result = await callFunction('shelf-management', {
          action: 'getShelfList',
          data: {
            pageSize: 1000
          }
        })
        if (result.code === 0) {
          const shelves = result.data.list
          this.shelfStats.total = shelves.length
          this.shelfStats.active = shelves.filter(s => s.is_active).length
          const stats = StateManager.getStateStats(shelves)
          this.shelfStats.rented = stats.rented
          this.shelfStats
        }
      } catch (error) {
        console.error('加载货架统计失败:', error)
      }
    },
    // 获取平台列表和统计数据
    async loadPlatformList() {
      try {
        const result = await callFunction('shelf-management', {
          action: 'getPlatformConfigs'
        })
        if (result.code === 0) {
          this.platformList = result.data
          this.platformStats.total = result.data.length
          this.platformStats.active = result.data.filter(c => c.login_status === 1).length
        }
      } catch (error) {
        console.error('加载平台列表失败:', error)
      }
    },
    async loadRecentLogs() {
      try {
        const result = await callFunction('shelf-management', {
          action: 'getOperationLogs',
          data: {
            pageSize: 5
          }
        })
        if (result.code === 0) {
          this.recentLogs = result.data.list
        }
      } catch (error) {
        console.error('加载最近日志失败:', error)
      }
    },
    goToLogs() {
      uni.switchTab({
        url: '/pages/operation-logs/index'
      })
    },
    getStatusClass(status) {
      switch (status) {
        case 1: return 'success'
        case 2: return 'error'
        default: return 'warning'
      }
    },
    getStatusText(status) {
      switch (status) {
        case 1: return '已登录'
        case 2: return '登录失效'
        default: return '未登录'
      }
    },
    getActionText(action) {
      const actionMap = {
        'login': '登录',
        'sync': '更新',
        'on_shelf': '上架',
        'off_shelf': '下架',
        'enable_monitor': '开启监控',
        'disable_monitor': '关闭监控'
      }
      return actionMap[action] || action
    },
    getPlatformName(platformType) {
      const platformMap = {
        'zuhaowan': '租号玩',
        'uhaozu': 'U号租'
      }
      return platformMap[platformType] || platformType
    },
    formatTime(timestamp) {
      if (!timestamp) return '从未'
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      if (diff < 60000) {
        return '刚刚'
      } else if (diff < 3600000) {
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < 86400000) {
        return Math.floor(diff / 3600000) + '小时前'
      } else {
        return date.toLocaleDateString()
      }
    },

    // 退出登录
    async handleLogout() {
      uni.showModal({
        title: '提示',
        content: '确定要退出登录吗？',
        success: async (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '退出中...'
            })
            try {
              await logout()
            } catch (error) {
              console.error('退出登录失败:', error)
            } finally {
              uni.hideLoading()
            }
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: $spacing-base; /* 底部留出tabBar空间 */
  background: linear-gradient(180deg, $bg-color-page 0%, $bg-color-container 100%);
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
}

/* 用户信息栏 */
.user-info {
  display: flex;
  align-items: center;
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-base;
  margin-bottom: $spacing-base;
  box-shadow: $shadow-base;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: $primary-gradient;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: $spacing-base;
}

.avatar-text {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  color: white;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  margin-bottom: 4rpx;
}

.user-phone {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.user-actions {
  margin-left: $spacing-base;
}

.title-text {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  letter-spacing: 1rpx;
}

.title-desc {
  font-size: 26rpx;
  color: #8c8c8c;
}

/* 区块样式 */
.status-section,
.platform-section,
.logs-section {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-base;
}

/* 区块标题 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: $spacing-base;
}

.header-left {
  flex: 1;
}

.section-title {
  font-size: $font-size-xl;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  margin-bottom: 4rpx;
  display: block;
}

.section-subtitle {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.more-link {
  font-size: $font-size-sm;
  color: $primary-color;
  font-weight: $font-weight-medium;

  &:active {
    opacity: 0.7;
  }
}

/* 状态卡片 */
.status-cards {
  display: flex;
  gap: $spacing-sm;
}

.status-card {
  flex: 1;
  background: linear-gradient(135deg, #f6f9fc 0%, #ffffff 100%);
  border-radius: $border-radius-base;
  padding: $spacing-base;
  border: 1rpx solid $border-color-light;
  position: relative;
  overflow: hidden;
  text-align: center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: $primary-gradient;
  }
}

.card-title {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  margin-bottom: 8rpx;
  font-weight: $font-weight-normal;
}

.card-value {
  font-size: $font-size-xxl;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin-bottom: 8rpx;
  display: block;
}

.card-desc {
  font-size: $font-size-xs;
  color: $text-color-placeholder;
}

/* 平台列表 */
.platform-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.platform-item {
  background: $bg-color-overlay;
  border-radius: $border-radius-base;
  padding: $spacing-base;
  border: 1rpx solid $border-color-light;
  transition: all $transition-base;

  &:active {
    background: $bg-color-hover;
  }
}

.platform-info {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.platform-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.platform-name {
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
}

.platform-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  margin-right: 8rpx;

  &.success {
    background-color: $success-color;
    box-shadow: 0 0 8rpx rgba(90, 199, 37, 0.3);
  }

  &.error {
    background-color: $error-color;
    box-shadow: 0 0 8rpx rgba(245, 108, 108, 0.3);
  }

  &.warning {
    background-color: $warning-color;
    box-shadow: 0 0 8rpx rgba(249, 174, 61, 0.3);
  }
}

.status-text {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  font-weight: $font-weight-medium;
}

.platform-meta {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.meta-text {
  font-size: $font-size-xs;
  color: $text-color-placeholder;
}

/* 日志列表 */
.log-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.log-item {
  background: $bg-color-overlay;
  border-radius: $border-radius-base;
  padding: $spacing-base;
  border: 1rpx solid $border-color-light;
  transition: all $transition-base;

  &:active {
    background: $bg-color-hover;
  }
}

.log-content {
  width: 100%;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.log-title {
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
}

.log-status {
  font-size: $font-size-xs;
  padding: 4rpx 12rpx;
  border-radius: $border-radius-base;
  font-weight: $font-weight-medium;

  &.success {
    background-color: $success-light;
    color: $success-color;
  }

  &.error {
    background-color: $error-light;
    color: $error-color;
  }
}

.log-desc {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.log-platform {
  font-size: $font-size-xs;
  color: $primary-color;
  background-color: $primary-color-light;
  padding: 4rpx 8rpx;
  border-radius: $border-radius-xs;
  font-weight: $font-weight-medium;
}

.log-separator {
  font-size: $font-size-sm;
  color: $border-color-base;
  margin: 0 8rpx;
}

.log-account {
  font-size: $font-size-sm;
  color: $text-color-secondary;
}

.log-time {
  font-size: $font-size-xs;
  color: $text-color-placeholder;
}
</style>