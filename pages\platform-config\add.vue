<template>
  <view class="container">
    <!-- 表单 -->
    <view class="form">
      <!-- 平台选择 -->
      <view class="form-item">
        <view class="form-label">选择平台</view>
        <view class="picker-input" @click="openPlatformPicker">
          {{ formData.platformName || '请选择平台' }}
          <uv-icon name="arrow-right" size="24" color="#999"></uv-icon>
        </view>
        <uv-picker
          round="16rpx"
          ref="platformPicker"
          itemHeight="70"
          :columns="[platformOptions]"
          keyName="name"
          @confirm="onPlatformChange"
          :disabled="isEdit"
        />
      </view>
      <!-- 登录方式选择 -->
      <view class="form-item" v-if="formData.platformType">
        <view class="form-label">登录方式</view>
        <uv-radio-group v-model="loginTypeValue" size="30" iconSize="20" labelSize="32" @change="onLoginTypeChange">
          <uv-radio 
            name="true"
            label="自动登录"
            activeColor="#3c9cff"
            :customStyle="{marginRight: '20rpx'}" 
          >
          </uv-radio>
          <uv-radio 
            name="false"
            label="手动配置"
            activeColor="#3c9cff"
          >
          </uv-radio>
        </uv-radio-group>
      </view>
      <!-- 自动登录配置 -->
      <template v-if="formData.autoLogin">
        <view class="form-item">
          <view class="form-label">用户名</view>
          <uv-input
            v-model="formData.username"
            placeholder="请输入平台登录用户名"
            maxlength="50"
            border="surround"
            :customStyle="{
              borderRadius: '12rpx',
              backgroundColor: '#f8f9fa'
            }"
          />
        </view>
        <view class="form-item">
          <view class="form-label">密码</view>
          <uv-input
            v-model="formData.password"
            placeholder="请输入平台登录密码"
            password
            maxlength="50"
            border="surround"
            :customStyle="{
              borderRadius: '12rpx',
              backgroundColor: '#f8f9fa'
            }"
          />
        </view>
      </template>
      <!-- 手动配置 -->
      <template v-else>
        <view class="form-item">
          <view class="form-label">Token</view>
          <uv-textarea
            v-model="formData.token"
            placeholder="请输入平台访问Token"
            maxlength="1000"
            :autoHeight="true"
            :customStyle="{
              borderRadius: '12rpx',
              backgroundColor: '#f8f9fa',
              border: '1px solid #dadbde'
            }"
          />
        </view>
        <view class="form-item">
          <view class="form-label">Cookie</view>
          <uv-textarea
            v-model="formData.cookie"
            placeholder="请输入平台Cookie信息"
            maxlength="2000"
            :autoHeight="true"
            :customStyle="{
              borderRadius: '12rpx',
              backgroundColor: '#f8f9fa',
              border: '1px solid #dadbde'
            }"
          />
        </view>
        <view class="form-item">
          <view class="form-label">请求头（可选）</view>
          <uv-textarea
            v-model="headersText"
            placeholder="请输入JSON格式的请求头，如：{&quot;User-Agent&quot;: &quot;...&quot;}"
            maxlength="1000"
            :autoHeight="true"
            :customStyle="{
              borderRadius: '12rpx',
              backgroundColor: '#f8f9fa',
              border: '1px solid #dadbde'
            }"
          />
        </view>
      </template>
      <!-- 备注 -->
      <view class="form-item">
        <view class="form-label">备注（可选）</view>
        <uv-textarea
          v-model="formData.remark"
          placeholder="请输入备注信息"
          maxlength="200"
          :autoHeight="true"
          :customStyle="{
            borderRadius: '12rpx',
            backgroundColor: '#f8f9fa',
            border: '1px solid #dadbde'
          }"
        />
      </view>
    </view>
    <!-- 操作按钮 -->
    <view class="actions">
      <uv-button
        type="primary"
        :loading="testLoading"
        :disabled="!canTest"
        @click="testLogin"
        :customStyle="{
          borderRadius: '100px'
        }"
        :text="testLoading ? '登录中...' : '登录'"
      >
      </uv-button>
      <uv-button
        type="success"
        :loading="saveLoading"
        :disabled="!canSave"
        @click="saveConfig"
        :customStyle="{
          borderRadius: '100px'
        }"
        :text="saveLoading ? '保存中...' : (isEdit ? '更新配置' : '保存配置')"
      >
      </uv-button>
      <uv-button
        type="info"
        @click="goBack"
        :customStyle="{
          borderRadius: '100px'
        }"
        text="返回"
      >
      </uv-button>
    </view>
    <!-- 帮助说明 -->
    <view class="help" v-if="formData.platformType">
      <view class="help-title">配置说明</view>
      <view class="help-content">
        <template v-if="formData.autoLogin">
          <text class="help-text">• 自动登录模式下，系统会使用您提供的账号密码自动登录平台</text>
          <text class="help-text">• Token会自动获取并存储，无需手动配置</text>
          <text class="help-text">• 登录状态会自动检测和续期</text>
        </template>
        <template v-else>
          <text class="help-text">• 手动配置模式下，您需要手动获取并配置Token和Cookie</text>
          <text class="help-text">• 请确保Token和Cookie的有效性</text>
          <text class="help-text">• 当登录失效时，需要手动更新配置</text>
        </template>
      </view>
    </view>
  </view>
</template>
<script>
import { callFunction } from '@/utils/request.js'

export default {
  name: 'PlatformConfigAdd',
  data() {
    return {
      isEdit: false,
      configId: '',
      platformOptions: [],
      platformIndex: 0,
      loginTypeValue: 'true',
      formData: {
        platformType: '',
        platformName: '',
        username: '',
        password: '',
        token: '',
        cookie: '',
        headers: {},
        autoLogin: true,
        remark: ''
      },
      headersText: '',
      testLoading: false,
      saveLoading: false
    }
  },
  computed: {
    canTest() {
      if (!this.formData.platformType) return false
      if (this.formData.autoLogin) {
        return this.formData.username && this.formData.password
      } else {
        return this.formData.token || this.formData.cookie
      }
    },
    canSave() {
      return this.canTest
    }
  },
  onLoad(options) {
    this.isEdit = options.type === 'edit'
    this.configId = options.id || ''
    this.loadPlatformOptions()
    if (this.isEdit && this.configId) {
      this.loadConfigData()
    }
  },
  methods: {
    async loadPlatformOptions() {
      try {
        const result = await callFunction('shelf-management', {
          action: 'getPlatformList'
        })
        if (result.code === 0) {
          this.platformOptions = result.data
        }
      } catch (error) {
        console.error('加载平台列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      }
    },
    async loadConfigData() {
      try {
        const result = await callFunction('shelf-management', {
          action: 'getPlatformConfigs'
        })
        if (result.code === 0) {
          const config = result.data.find(item => item._id === this.configId)
          if (config) {
            this.formData = {
              platformType: config.platform_type,
              platformName: config.platform_name,
              username: config.username,
              autoLogin: config.auto_login,
              remark: config.remark
            }
            // 设置平台选择器的索引
            this.platformIndex = this.platformOptions.findIndex(
              p => p.value === config.platform_type
            )
            // 设置登录方式
            this.loginTypeValue = config.auto_login ? 'true' : 'false'
          }
        }
      } catch (error) {
        console.error('加载配置数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      }
    },
    onPlatformChange(e) {
      const index = e.indexs[0]
      this.platformIndex = index
      const platform = this.platformOptions[index]
      console.log(platform)
      this.formData.platformType = platform.type
      this.formData.platformName = platform.name
    },
    onLoginTypeChange(e) {
      this.formData.autoLogin = e === 'true'
      this.loginTypeValue = e
      // 切换登录方式时清空相关字段
      if (this.formData.autoLogin) {
        this.formData.token = ''
        this.formData.cookie = ''
        this.formData.headers = {}
        this.headersText = ''
      } else {
        this.formData.username = ''
        this.formData.password = ''
      }
    },
    goBack() {
      uni.navigateBack()
    },
    async testLogin() {
      this.testLoading = true
      try {
        // 解析请求头
        let headers = {}
        if (this.headersText.trim()) {
          try {
            headers = JSON.parse(this.headersText)
          } catch (error) {
            uni.showToast({
              title: '请求头格式错误',
              icon: 'error'
            })
            return
          }
        }
        const result = await callFunction('shelf-management', {
          action: 'testPlatformLogin',
          data: {
            configId: this.configId, // {{ AURA-X: Add - 传递配置ID用于精确更新（编辑模式）. Approval: 寸止(ID:1735373000). }}
            platformType: this.formData.platformType,
            username: this.formData.username,
            password: this.formData.password,
            token: this.formData.token,
            cookie: this.formData.cookie,
            headers: headers
          }
        })
        if (result.code === 0) {
          uni.showToast({
            title: '登录成功',
            icon: 'success'
          })
        } else {
          uni.showModal({
            title: '登录失败',
            content: result.message,
            showCancel: false
          })
        }
      } catch (error) {
        console.error('登录失败:', error)
        uni.showToast({
          title: '登录失败',
          icon: 'error'
        })
      } finally {
        this.testLoading = false
      }
    },
    async saveConfig() {
      this.saveLoading = true
      try {
        // 解析请求头
        let headers = {}
        if (this.headersText.trim()) {
          try {
            headers = JSON.parse(this.headersText)
          } catch (error) {
            uni.showToast({
              title: '请求头格式错误',
              icon: 'error'
            })
            return
          }
        }
        const result = await callFunction('shelf-management', {
          action: 'savePlatformConfig',
          data: {
            platformType: this.formData.platformType,
            platformName: this.formData.platformName,
            username: this.formData.username,
            password: this.formData.password,
            token: this.formData.token,
            cookie: this.formData.cookie,
            headers: headers,
            autoLogin: this.formData.autoLogin,
            remark: this.formData.remark
          }
        })
        if (result.code === 0) {
          uni.showToast({
            title: this.isEdit ? '更新成功' : '保存成功',
            icon: 'success'
          })
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } else {
          throw new Error(result.message)
        }
      } catch (error) {
        console.error('保存配置失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        })
      } finally {
        this.saveLoading = false
      }
    },
    openPlatformPicker() {
      this.$refs.platformPicker.open()
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: $spacing-base;
  background: $bg-color-page;
  height: 100%;
  overflow-y: auto;
}

.form {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  margin-bottom: $spacing-lg;
  box-shadow: $shadow-base;
}

.form-item {
  margin-bottom: $spacing-xl;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  font-size: $font-size-base;
  color: $text-color-primary;
  margin-bottom: $spacing-sm;
  font-weight: $font-weight-medium;
}

.picker-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 76rpx;
  padding: 0 $spacing-base;
  border: 2rpx solid $border-color-base;
  border-radius: $border-radius-sm;
  font-size: $font-size-base;
  background: $bg-color-container;
  transition: all $transition-base;

  &:active {
    border-color: $primary-color;
  }
}
.actions {
  display: flex;
  flex-direction: column;
  gap: $spacing-base;
}

.help {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-base;
}

.help-title {
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  color: $text-color-primary;
  margin-bottom: $spacing-base;
}

.help-content {
  display: flex;
  flex-direction: column;
}

.help-text {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  line-height: 1.6;
  margin-bottom: 12rpx;

  &:last-child {
    margin-bottom: 0;
  }
}
</style>