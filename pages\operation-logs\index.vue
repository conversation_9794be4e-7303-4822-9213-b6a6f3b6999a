<template>
  <view class="container">
    <!-- 筛选条件 -->
    <view class="filter-bar">
      <view class="filter-item">
        <view class="picker-input" @click="openPlatformPicker">
          {{ platformOptions[platformIndex].name || '全部平台' }}
          <uv-icon name="arrow-down" size="24" color="#999"></uv-icon>
        </view>
        <uv-picker
          round="16rpx"
          ref="platformPicker"
          itemHeight="70"
          :columns="[platformOptions]"
          keyName="name"
          @confirm="onPlatformChange"
        />
      </view>
      <view class="filter-item">
        <view class="picker-input" @click="openActionPicker">
          {{ actionOptions[actionIndex].name || '全部操作' }}
          <uv-icon name="arrow-down" size="24" color="#999"></uv-icon>
        </view>
        <uv-picker
          round="16rpx"
          ref="actionPicker"
          itemHeight="70"
          :columns="[actionOptions]"
          keyName="name"
          @confirm="onActionChange"
        />
      </view>
      <view class="filter-item">
        <view class="picker-input" @click="openStatusPicker">
          {{ statusOptions[statusIndex].name || '全部状态' }}
          <uv-icon name="arrow-down" size="24" color="#999"></uv-icon>
        </view>
        <uv-picker
          round="16rpx"
          ref="statusPicker"
          itemHeight="70"
          :columns="[statusOptions]"
          keyName="name"
          @confirm="onStatusChange"
        />
      </view>
    </view>
    <!-- 统计信息 -->
    <view class="stats">
      <view class="stat-item">
        <text class="stat-value">{{ logList.length }}</text>
        <text class="stat-label">当前显示</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ successCount }}</text>
        <text class="stat-label">成功</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ failCount }}</text>
        <text class="stat-label">失败</text>
      </view>
    </view>
    <!-- 日志列表 -->
    <view class="log-list">
      <view
        v-for="log in logList"
        :key="log._id"
        class="log-item"
        @click="showLogDetail(log)"
      >
        <view class="log-header">
          <view class="log-info">
            <text class="log-action">{{ getActionText(log.action) }}</text>
            <view class="log-tags">
              <text class="platform-tag">{{ getPlatformName(log.platform_type) }}</text>
              <text
                class="status-tag"
                :class="log.status ? 'success' : 'error'"
              >
                {{ log.status ? '成功' : '失败' }}
              </text>
              <text class="trigger-tag" :class="log.trigger_type">
                {{ getTriggerText(log.trigger_type) }}
              </text>
            </view>
          </view>
          <view class="log-time">
            {{ formatTime(log.create_time) }}
          </view>
        </view>
        <view class="log-content">
          <view class="log-detail" v-if="log.game_account">
            <text class="detail-label">账号：</text>
            <text class="detail-value">{{ log.game_account }}</text>
          </view>
          <view class="log-detail">
            <text class="detail-label">消息：</text>
            <text class="detail-value" :class="log.status ? 'success-text' : 'error-text'">
              {{ log.message }}
            </text>
          </view>
          <view class="log-detail" v-if="log.error_code">
            <text class="detail-label">错误码：</text>
            <text class="detail-value error-text">{{ log.error_code }}</text>
          </view>
          <view class="log-detail" v-if="log.execution_time">
            <text class="detail-label">耗时：</text>
            <text class="detail-value">{{ log.execution_time }}ms</text>
          </view>
        </view>
      </view>
      <!-- 空状态 -->
      <view v-if="logList.length === 0 && !loading" class="empty-state">
        <text class="empty-icon">📋</text>
        <text class="empty-text">暂无操作日志</text>
        <text class="empty-desc">系统操作日志将在这里显示</text>
      </view>
    </view>
    <!-- 加载状态 -->
    <view v-if="loading" class="loading">
      <uv-loading-icon mode="circle" size="40"></uv-loading-icon>
      <text style="margin-left: 20rpx;">加载中...</text>
    </view>
    <!-- 加载更多 -->
    <view v-if="hasMore && !loading" class="load-more">
      <uv-button
        type="info"
        :loading="isLoadingMore"
        @click="loadMore"
        :customStyle="{
          borderRadius: '20rpx'
        }"
        :text="isLoadingMore ? '加载中...' : '加载更多'"
      >
      </uv-button>
    </view>

    <!-- 简化版tabBar -->
    <simple-tabbar
      :current="currentTabIndex"
      @change="onTabChange"
    ></simple-tabbar>
  </view>
</template>
<script>
import { callFunction } from '@/utils/request.js'
import simpleTabBarMixin from '@/mixins/simple-tabbar-mixin.js'

export default {
  name: 'OperationLogs',
  mixins: [simpleTabBarMixin],
  data() {
    return {
      logList: [],
      loading: false,
      isLoadingMore: false,
      hasMore: true,
      currentPage: 1,
      pageSize: 20,
      currentTabIndex: 3, // 日志页面是第4个tab，索引为3
      // 筛选选项
      platformOptions: [
        { name: '全部平台', value: '' }
        // {{ AURA-X: Modify - 移除硬编码平台列表，改为动态获取. Approval: 寸止(ID:1735372900). }}
      ],
      actionOptions: [
        { name: '全部操作', value: '' },
        { name: '上架', value: 'shelf_on' },
        { name: '下架', value: 'shelf_off' },
        { name: '更新', value: 'sync' }
      ],
      statusOptions: [
        { name: '全部状态', value: '' },
        { name: '成功', value: 1 },
        { name: '失败', value: 0 }
      ],
      platformIndex: 0,
      actionIndex: 0,
      statusIndex: 0,
      selectedPlatform: '',
      selectedAction: '',
      selectedStatus: ''
    }
  },
  computed: {
    successCount() {
      return this.logList.filter(log => log.status === 1).length
    },
    failCount() {
      return this.logList.filter(log => log.status === 0).length
    },
    filterParams() {
      const params = {}

      if (this.selectedPlatform) {
        params.platformType = this.selectedPlatform
      }

      if (this.selectedAction) {
        params.action = this.selectedAction
      }

      if (this.selectedStatus !== '') {
        params.status = this.selectedStatus
      }

      return params
    }
  },
  onShow() {
    // {{ AURA-X: Modify - 页面显示时先加载平台列表，再加载日志列表. Approval: 寸止(ID:1735372900). }}
    this.loadPlatformOptions()
    this.loadLogList()
  },

  onReachBottom() {
    this.loadMore()
  },
  methods: {
    // {{ AURA-X: Add - 新增动态加载平台选项的方法. Approval: 寸止(ID:1735372900). }}
    async loadPlatformOptions() {
      try {
        const result = await callFunction('shelf-management', {
          action: 'getPlatformList'
        })
        if (result.code === 0) {
          // 转换API返回的数据格式为选择器需要的格式
          const dynamicPlatforms = result.data.map(platform => ({
            name: platform.name,
            value: platform.type
          }))
          // 保持"全部平台"选项在第一位，然后添加动态获取的平台
          this.platformOptions = [
            { name: '全部平台', value: '' },
            ...dynamicPlatforms
          ]
        } else {
          console.error('获取平台列表失败:', result.message)
        }
      } catch (error) {
        console.error('加载平台选项失败:', error)
        uni.showToast({
          title: '平台列表加载失败',
          icon: 'none',
          duration: 2000
        })
      }
    },

    async loadLogList(isLoadMore = false) {
      if (isLoadMore) {
        this.isLoadingMore = true
      } else {
        this.loading = true
        this.currentPage = 1
        this.hasMore = true
      }
      try {
        const result = await callFunction('shelf-management', {
          action: 'getOperationLogs',
          data: {
            ...this.filterParams,
            pageIndex: this.currentPage,
            pageSize: this.pageSize
          }
        })

        if (result.code === 0) {
          const newLogs = result.data.list
          if (isLoadMore) {
            this.logList = [...this.logList, ...newLogs]
          } else {
            this.logList = newLogs
          }
          // 判断是否还有更多数据
          this.hasMore = newLogs.length === this.pageSize
          if (this.hasMore) {
            this.currentPage++
          }
        } else {
          throw new Error(result.message)
        }
      } catch (error) {
        console.error('加载日志失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
        this.isLoadingMore = false
      }
    },

    loadMore() {
      if (this.hasMore && !this.loading && !this.isLoadingMore) {
        this.loadLogList(true)
      }
    },

    onPlatformChange(e) {
      this.platformIndex = e.indexs[0]
      this.selectedPlatform = e.value[0].value
      this.loadLogList()
    },
    onActionChange(e) {
      this.actionIndex = e.indexs[0]
      this.selectedAction = e.value[0].value
      this.loadLogList()
    },
    onStatusChange(e) {
      this.statusIndex = e.indexs[0]
      this.selectedStatus = e.value[0].value
      this.loadLogList()
    },
    showLogDetail(log) {
      const details = []
      if (log.platform_shelf_id) {
        details.push(`货架ID: ${log.platform_shelf_id}`)
      }
      if (log.request_data) {
        details.push(`请求数据: ${JSON.stringify(log.request_data)}`)
      }
      if (log.response_data) {
        details.push(`响应数据: ${JSON.stringify(log.response_data)}`)
      }
      uni.showModal({
        title: '日志详情',
        content: details.join('\n\n') || '暂无详细信息',
        showCancel: false
      })
    },
    getPlatformName(platformType) {
      // {{ AURA-X: Modify - 使用动态平台数据替代硬编码映射. Approval: 寸止(ID:1735372900). }}
      // 优先从动态获取的平台选项中查找
      const platform = this.platformOptions.find(p => p.value === platformType)
      if (platform) {
        return platform.name
      }

      // 备用映射（包含系统等特殊类型）
      const fallbackMap = {
        'system': '系统'
      }
      return fallbackMap[platformType] || platformType
    },
    getActionText(action) {
      const actionMap = {
        'login': '登录',
        'sync': '更新',
        'on_shelf': '上架',
        'off_shelf': '下架',
        'monitor_task': '监控任务',
        'process_user_platforms': '处理用户平台',
        'create_adapter': '创建适配器'
      }
      return actionMap[action] || action
    },
    getTriggerText(triggerType) {
      const triggerMap = {
        'auto': '自动',
        'manual': '手动'
      }
      return triggerMap[triggerType] || triggerType
    },
    formatTime(timestamp) {
      if (!timestamp) return '未知时间'
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      if (diff < 60000) {
        return '刚刚'
      } else if (diff < 3600000) {
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < 86400000) {
        return Math.floor(diff / 3600000) + '小时前'
      } else if (diff < **********) {
        return Math.floor(diff / 86400000) + '天前'
      } else {
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
      }
    },
    openPlatformPicker() {
      this.$refs.platformPicker.open()
    },
    openActionPicker() {
      this.$refs.actionPicker.open()
    },
    openStatusPicker() {
      this.$refs.statusPicker.open()
    }
  }
}
</script>
<style scoped lang="scss">
.container {
  padding: $spacing-base; /* 底部留出tabBar空间 */
  background: $bg-color-page;
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
}

.filter-bar {
  display: flex;
  gap: $spacing-base;
  margin-bottom: $spacing-base;
}

.filter-item {
  flex: 1;
}

.picker-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60rpx;
  padding: 0 $spacing-base;
  background: $bg-color-container;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
  box-shadow: $shadow-sm;
}

.stats {
  display: flex;
  gap: $spacing-sm;
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-base;
  margin-bottom: $spacing-base;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  font-size: $font-size-xxl;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin-bottom: 8rpx;
  display: block;
}

.stat-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  font-weight: $font-weight-normal;
}
.log-list {
  display: flex;
  flex-direction: column;
}

.log-item {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  box-shadow: $shadow-base;
  margin-bottom: $spacing-base;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: $spacing-base;
}

.log-info {
  flex: 1;
}

.log-action {
  font-size: $font-size-base;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
  margin-bottom: 12rpx;
}

.log-tags {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-wrap: wrap;
}

.platform-tag {
  font-size: $font-size-xs;
  color: $primary-color;
  background-color: $primary-color-light;
  padding: 4rpx 8rpx;
  border-radius: $border-radius-xs;
  font-weight: $font-weight-medium;
}

.status-tag {
  font-size: $font-size-xs;
  padding: 4rpx 12rpx;
  border-radius: $border-radius-base;
  font-weight: $font-weight-medium;
  display: inline-block;

  &.success {
    background-color: $success-light;
    color: $success-color;
  }

  &.error {
    background-color: $error-light;
    color: $error-color;
  }
}

.trigger-tag {
  font-size: $font-size-xs;
  padding: 4rpx 12rpx;
  border-radius: $border-radius-base;
  font-weight: $font-weight-medium;
  display: inline-block;
  background-color: $info-light;
  color: $info-color;

  &.auto {
    background-color: $warning-light;
    color: $warning-color;
  }

  &.manual {
    background-color: $success-light;
    color: $success-color;
  }
}

.log-time {
  font-size: $font-size-xs;
  color: $text-color-placeholder;
  margin-left: $spacing-base;
  white-space: nowrap;
}
.log-content {
  margin-top: $spacing-base;
}

.log-detail {
  display: flex;
  align-items: flex-start;
  min-width: 0;
  flex: 1;
  margin-bottom: 12rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  margin-right: 8rpx;
  white-space: nowrap;
  min-width: 80rpx;
}

.detail-value {
  font-size: $font-size-sm;
  color: $text-color-primary;
  word-break: break-all;

  &.success-text {
    color: $success-color;
  }

  &.error-text {
    color: $error-color;
  }
}

.empty-state {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  box-shadow: $shadow-base;
  margin-bottom: $spacing-base;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: $spacing-lg;
  color: $text-color-placeholder;
}

.empty-text {
  font-size: $font-size-lg;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
  font-weight: $font-weight-medium;
}

.empty-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.load-more {
  text-align: center;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
  color: $text-color-secondary;
}

</style>