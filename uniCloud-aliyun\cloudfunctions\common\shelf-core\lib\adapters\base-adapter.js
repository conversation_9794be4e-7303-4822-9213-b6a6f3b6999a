'use strict'
const axios = require('axios')

/**
 * 平台适配器基类
 * 定义所有平台适配器必须实现的接口
 */
class BaseAdapter {
  constructor(config) {
    console.log('BaseAdapter constructor', config)
    this.config = config
    this.platformType = config.platform_type
    this.platformName = config.platform_name
    this.token = config.token
    this.cookie = config.cookie
    this.headers = config.headers || {}
    this.autoLogin = config.auto_login || false
    this.username = config.username
    this.password = config.password

    // {{ AURA-X: Modify - 优化请求处理机制，移除强制间隔限制，增加统一延迟返回. Approval: 寸止(ID:1735372900). }}
    this.requestQueue = []
    this.isProcessingQueue = false
    this.maxRetries = 3 // 最大重试次数
    this.retryDelay = 2000 // 重试延迟2秒
    this.responseDelay = 5000 // API响应后统一延迟5秒返回
  }

  /**
   * 登录平台获取访问令牌
   * 子类必须实现此方法
   * @returns {Promise<Object>} 登录结果 { success: boolean, token: string, message: string }
   */
  async login() {
    throw new Error('子类必须实现 login 方法')
  }

  /**
   * 检查登录状态
   * {{ AURA-X: Modify - 简化登录状态检查，统一使用token存在性判断. Approval: 寸止(ID:1735372900). }}
   * @returns {Promise<boolean>} 是否已登录
   */
  async checkLoginStatus() {
    // 简单检查token是否存在，实际登录状态检测在executeRequest中统一处理
    return this.token && this.token.length > 0
  }

  /**
   * 获取货架列表
   * 子类必须实现此方法
   * @returns {Promise<Array>} 货架列表
   */
  async getShelfList() {
    throw new Error('子类必须实现 getShelfList 方法')
  }

  /**
   * 上架货架
   * 子类必须实现此方法
   * @param {string} shelfId 货架ID
   * @returns {Promise<Object>} 操作结果 { success: boolean, message: string }
   */
  async onShelf(shelfId) {
    throw new Error('子类必须实现 onShelf 方法')
  }

  /**
   * 下架货架
   * 子类必须实现此方法
   * @param {string} shelfId 货架ID
   * @returns {Promise<Object>} 操作结果 { success: boolean, message: string }
   */
  async offShelf(shelfId) {
    throw new Error('子类必须实现 offShelf 方法')
  }

  /**
   * 发送HTTP请求的通用方法 (支持重试机制和统一延迟返回)
   * 注意：请求会立即发送到第三方API，但响应会在5秒后返回给调用方
   * @param {string} url 请求URL
   * @param {Object} options 请求选项
   * @returns {Promise<Object>} 响应数据 (API响应后延迟5秒返回)
   */
  async request(url, options = {}) {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ url, options, resolve, reject, retries: 0 })
      this.processQueue()
    })
  }

  /**
   * 处理请求队列 (优化版本：移除强制间隔，支持并发处理)
   */
  async processQueue() {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return
    }

    this.isProcessingQueue = true

    // {{ AURA-X: Modify - 移除强制间隔限制，允许并发请求处理. Approval: 寸止(ID:1735372900). }}
    while (this.requestQueue.length > 0) {
      const requestItem = this.requestQueue.shift()

      // 异步处理每个请求，不阻塞队列
      this.processRequest(requestItem)
    }

    this.isProcessingQueue = false
  }

  /**
   * 处理单个请求 (支持重试和统一延迟返回)
   * @param {Object} requestItem 请求项
   */
  async processRequest(requestItem) {
    try {
      // {{ AURA-X: Add - 执行请求并实现统一延迟返回机制. Approval: 寸止(ID:1735372900). }}
      const response = await this.executeRequest(requestItem.url, requestItem.options)

      // API响应成功后，延迟5秒再返回给调用方
      setTimeout(() => {
        console.log(`延迟返回响应: ${requestItem.url}`)
        requestItem.resolve(response)
      }, this.responseDelay)

    } catch (error) {
      // 检查是否需要重试
      if (requestItem.retries < this.maxRetries && this.shouldRetry(error)) {
        requestItem.retries++
        console.log(`请求失败，准备第${requestItem.retries}次重试: ${requestItem.url}`)

        // 延迟后重新处理请求
        setTimeout(() => {
          this.processRequest(requestItem)
        }, this.retryDelay * requestItem.retries)
      } else {
        // 错误情况下立即返回，不延迟
        requestItem.reject(error)
      }
    }
  }

  /**
   * 执行实际的HTTP请求 (立即发送到第三方API)
   * @param {string} url 请求URL
   * @param {Object} options 请求选项
   * @returns {Promise<Object>} 响应数据 (注意：调用方会在5秒后收到此响应)
   */
  async executeRequest(url, options = {}) {
    try {
      const defaultHeaders = {
        ...this.headers
      }

      // 添加认证信息
      if (this.token) {
        // 子类可以重写此方法来设置特定的认证头
      }
      if (this.cookie) {
        defaultHeaders['Cookie'] = this.cookie
      }

      const axiosConfig = {
        method: options.method || 'GET',
        url: url,
        headers: {
          ...defaultHeaders,
          ...options.headers
        },
        timeout: options.timeout || 30000,
        data: options.data,
        validateStatus: function (status) {
          return status >= 200 && status < 300
        }
      }

      console.log(`发送请求: ${axiosConfig.method} ${url}`)

      const response = await axios(axiosConfig)

      console.log(`请求成功: ${url}`)

      // {{ AURA-X: Add - 统一登录状态检测逻辑. Approval: 寸止(ID:1735372900). }}
      // 检查登录状态
      if (this.isLoginExpired(response.data)) {
        console.log(`检测到${this.platformType}登录已过期`)
        if (this.autoLogin && this.username && this.password) {
          console.log(`尝试自动重新登录${this.platformType}`)
          const loginResult = await this.login()
          if (loginResult.success) {
            this.token = loginResult.token
            console.log(`${this.platformType}自动重新登录成功`)
          } else {
            throw new Error(`${this.platformType}自动重新登录失败: ${loginResult.message}`)
          }
        } else {
          throw new Error(`${this.platformType}登录已过期，请重新登录`)
        }
      }

      // 检查是否需要刷新token
      await this.checkAndRefreshToken(response.data)

      return response.data
    } catch (error) {
      console.error(`请求失败: ${url}`, error.message)

      // 检查是否是认证失败，需要重新登录
      if (this.isAuthError(error)) {
        console.log('检测到认证失败，尝试自动重新登录')
        await this.handleAuthError()
      }

      if (error.response) {
        throw new Error(`HTTP请求失败: ${error.response.status} ${error.response.statusText}`)
      } else if (error.request) {
        throw new Error('网络请求超时或无响应')
      } else {
        throw new Error(`请求配置错误: ${error.message}`)
      }
    }
  }

  /**
   * 判断是否应该重试
   * @param {Error} error 错误对象
   * @returns {boolean} 是否应该重试
   */
  shouldRetry(error) {
    // 网络错误或5xx服务器错误可以重试
    return error.message.includes('网络') ||
           error.message.includes('超时') ||
           (error.response && error.response.status >= 500)
  }

  /**
   * 检查是否是认证错误
   * @param {Error} error 错误对象
   * @returns {boolean} 是否是认证错误
   */
  isAuthError(error) {
    return error.response && (error.response.status === 401 || error.response.status === 403)
  }

  /**
   * 处理认证错误 (自动重新登录)
   */
  async handleAuthError() {
    if (this.autoLogin && this.username && this.password) {
      try {
        const loginResult = await this.login()
        if (loginResult.success) {
          console.log('自动重新登录成功')
        }
      } catch (error) {
        console.error('自动重新登录失败:', error)
      }
    }
  }

  /**
   * 检查登录是否过期
   * @param {Object} responseData 响应数据
   * @returns {boolean} 是否登录过期
   */
  isLoginExpired(responseData) {
    // 子类必须重写此方法来实现特定平台的登录状态检测
    return false
  }

  /**
   * 检查并刷新token
   * @param {Object} responseData 响应数据
   */
  async checkAndRefreshToken(responseData) {
    // 子类可以重写此方法来实现特定平台的token刷新逻辑
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 统一状态码转换
   * 将各平台的状态码转换为统一的状态码
   * @param {any} platformStatus 平台原始状态
   * @returns {Object} 统一状态 { unified_state: number }
   */
  convertStatus(platformStatus) {
    // 子类可以重写此方法实现平台特定的状态转换
    return {
      unified_state: 0, // 0待租，1出租中，-1下架，-2其他
      platform_status: platformStatus
    }
  }

  /**
   * 从货架列表中查找单个货架状态
   * @param {string} shelfId 货架ID
   * @param {Array} shelfList 货架列表（可选，如果不提供则调用getShelfList）
   * @returns {Promise<Object>} 货架状态
   */
  async getShelfStatusFromList(shelfId, shelfList = null) {
    try {
      // 如果没有提供货架列表，则获取完整列表
      const shelves = shelfList || await this.getShelfList()
      const shelf = shelves.find(item => item.id === shelfId)

      if (!shelf) {
        throw new Error(`未找到货架 ID: ${shelfId}`)
      }

      return {
        unified_state: shelf.unified_state,
        platform_status: shelf.platform_status
      }
    } catch (error) {
      console.error(`从列表中获取货架状态失败 (${this.platformType}):`, error)
      throw error
    }
  }

  /**
   * 获取平台信息
   * @returns {Object} 平台信息
   */
  getPlatformInfo() {
    return {
      type: this.platformType,
      name: this.platformName,
      autoLogin: this.autoLogin
    }
  }
}

module.exports = BaseAdapter
